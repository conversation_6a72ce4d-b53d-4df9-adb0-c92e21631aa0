"""
Web-compatible main entry point for the Simple Roguelike game.
This version uses async/await pattern required by pygbag for web deployment.
"""

import asyncio
import pygame
import sys
import logging
from game_web import GameWeb

# Configure logging for web environment
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Global game instance
game = None

async def main():
    """
    Main async game loop for web deployment.
    This replaces the traditional synchronous game loop.
    """
    global game

    try:
        # Initialize the game
        logger.info("Initializing game for web deployment...")
        game = GameWeb()
        logger.info("Game initialized successfully")

        # Main game loop - must be async for web compatibility
        while game.running:
            try:
                # Handle events
                for event in pygame.event.get():
                    if event.type == pygame.QUIT:
                        game.running = False
                        logger.info("Quit event received")

                    # Handle mouse clicks
                    if event.type == pygame.MOUSEBUTTONDOWN:
                        if event.button == 1:  # Left mouse button
                            game._handle_left_click(event)

                    # Handle mouse button release
                    elif event.type == pygame.MOUSEBUTTONUP:
                        if event.button == 1:  # Left mouse button
                            # Stop continuous shooting
                            game.continuous_shooting = False

                    # Handle keyboard input
                    elif event.type == pygame.KEYDOWN:
                        game._handle_keydown(event)

                    elif event.type == pygame.KEYUP:
                        game._handle_keyup(event)

                    # Handle mouse wheel for zoom
                    elif event.type == pygame.MOUSEWHEEL:
                        game._handle_mouse_wheel(event)

                # Update game state
                game.update()

                # Draw everything
                game.draw()

                # FPS monitoring
                game._update_fps_counter()

                # Cap the frame rate
                game.clock.tick(60)  # Fixed 60 FPS for web

                # CRITICAL: This await is required for web deployment
                # It allows the browser to handle other tasks and prevents blocking
                await asyncio.sleep(0)

            except Exception as e:
                logger.error(f"Error in main game loop: {e}")
                # Continue running but log the error
                continue

    except Exception as e:
        logger.error(f"Fatal error in main: {e}")

    finally:
        # Clean up
        logger.info("Cleaning up and exiting")
        if game and hasattr(game, 'audio_manager'):
            game.audio_manager.cleanup()
        pygame.quit()

if __name__ == "__main__":
    # For web deployment, we use asyncio.run()
    # This is non-blocking on pygame-wasm
    asyncio.run(main())

    # Note: Do not add anything after asyncio.run()
    # The code would be executed right before program start main()
