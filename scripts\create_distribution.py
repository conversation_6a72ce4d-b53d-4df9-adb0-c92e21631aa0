#!/usr/bin/env python3
"""
Distribution package creator for Simple Roguelike Game.

This script creates a clean distribution package ready for itch.io upload.
"""

import os
import shutil
import zipfile
import sys
from pathlib import Path
import datetime

def create_distribution_directory():
    """Create a clean distribution directory."""
    dist_dir = "dist"
    if os.path.exists(dist_dir):
        shutil.rmtree(dist_dir)
    os.makedirs(dist_dir)
    return dist_dir

def copy_essential_files(dist_dir):
    """Copy essential game files to distribution directory."""
    print("Copying essential files...")
    
    # Essential files to include
    essential_files = [
        'main.py',
        'game.py', 
        'config.py',
        'requirements.txt',
        'README.md',
        'LICENSE',
        'CHANGELOG.md',
        'DISTRIBUTION_INFO.md',
        'run_tests.py'
    ]
    
    for file in essential_files:
        if os.path.exists(file):
            shutil.copy2(file, dist_dir)
            print(f"  Copied: {file}")
        else:
            print(f"  WARNING: Missing {file}")

def copy_directories(dist_dir):
    """Copy essential directories to distribution."""
    print("Copying directories...")
    
    # Directories to include
    directories = [
        'assets',
        'docs', 
        'entities',
        'level',
        'progression',
        'systems',
        'ui',
        'utils',
        'scripts'
    ]
    
    for directory in directories:
        if os.path.exists(directory):
            dest_path = os.path.join(dist_dir, directory)
            shutil.copytree(directory, dest_path)
            print(f"  Copied: {directory}/")
        else:
            print(f"  WARNING: Missing {directory}/")

def copy_tests_selectively(dist_dir):
    """Copy only essential tests to distribution."""
    print("Copying essential tests...")
    
    tests_dir = os.path.join(dist_dir, 'tests')
    os.makedirs(tests_dir)
    
    # Copy test runner and essential test files
    essential_tests = [
        'tests/__init__.py',
        'tests/test_player.py',
        'tests/test_equipment.py', 
        'tests/test_progression.py',
        'tests/test_skill_tree.py'
    ]
    
    for test_file in essential_tests:
        if os.path.exists(test_file):
            dest_path = os.path.join(dist_dir, test_file)
            os.makedirs(os.path.dirname(dest_path), exist_ok=True)
            shutil.copy2(test_file, dest_path)
            print(f"  Copied: {test_file}")

def create_distribution_readme(dist_dir):
    """Create a distribution-specific README."""
    print("Creating distribution README...")
    
    readme_content = """# Simple Roguelike - Distribution Package

## Quick Start
1. Install Python 3.7+ from https://python.org
2. Open terminal/command prompt in this directory
3. Run: `pip install -r requirements.txt`
4. Run: `python main.py`

## Alternative Launch (Windows)
- Double-click: `scripts/run_game_simple.ps1`

## Verify Installation
- Run: `python run_tests.py`

## Documentation
- See `README.md` for complete documentation
- See `docs/` folder for detailed guides
- Press F1 in-game for help

## Support
- All source code included for learning/modification
- MIT License - see LICENSE file
- No DRM, no online requirements

## Credits
- Author: sb (Developer)
- Designer: Jozzpoly (Game Design)
- Engine: Python + Pygame

Enjoy the game!
"""
    
    with open(os.path.join(dist_dir, 'QUICK_START.md'), 'w', encoding='utf-8') as f:
        f.write(readme_content)
    print("  Created: QUICK_START.md")

def create_zip_package(dist_dir):
    """Create a ZIP package for distribution."""
    print("Creating ZIP package...")
    
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    zip_filename = f"simple_roguelike_v4.0_{timestamp}.zip"
    
    with zipfile.ZipFile(zip_filename, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for root, dirs, files in os.walk(dist_dir):
            for file in files:
                file_path = os.path.join(root, file)
                arc_path = os.path.relpath(file_path, dist_dir)
                zipf.write(file_path, arc_path)
                
    print(f"  Created: {zip_filename}")
    return zip_filename

def verify_distribution(dist_dir):
    """Verify the distribution package is complete."""
    print("Verifying distribution package...")
    
    # Check essential files
    essential_files = ['main.py', 'requirements.txt', 'README.md']
    missing_files = []
    
    for file in essential_files:
        if not os.path.exists(os.path.join(dist_dir, file)):
            missing_files.append(file)
    
    # Check essential directories  
    essential_dirs = ['assets', 'entities', 'systems']
    missing_dirs = []
    
    for directory in essential_dirs:
        if not os.path.exists(os.path.join(dist_dir, directory)):
            missing_dirs.append(directory)
    
    if missing_files or missing_dirs:
        print(f"  ERROR: Missing files: {missing_files}")
        print(f"  ERROR: Missing directories: {missing_dirs}")
        return False
    else:
        print("  Distribution package verified successfully!")
        return True

def calculate_package_size(dist_dir):
    """Calculate the total size of the distribution package."""
    total_size = 0
    file_count = 0
    
    for root, dirs, files in os.walk(dist_dir):
        for file in files:
            file_path = os.path.join(root, file)
            total_size += os.path.getsize(file_path)
            file_count += 1
    
    # Convert to MB
    size_mb = total_size / (1024 * 1024)
    print(f"  Package size: {size_mb:.1f} MB ({file_count} files)")
    return size_mb

def main():
    """Main distribution creation function."""
    print("=" * 60)
    print("SIMPLE ROGUELIKE - DISTRIBUTION PACKAGE CREATOR")
    print("=" * 60)
    
    # Ensure we're in the project root
    if not os.path.exists('main.py'):
        print("ERROR: Must run from project root directory")
        return 1
    
    try:
        # Create distribution
        dist_dir = create_distribution_directory()
        print(f"Created distribution directory: {dist_dir}")
        print()
        
        # Copy files and directories
        copy_essential_files(dist_dir)
        print()
        
        copy_directories(dist_dir)
        print()
        
        copy_tests_selectively(dist_dir)
        print()
        
        create_distribution_readme(dist_dir)
        print()
        
        # Verify and package
        if verify_distribution(dist_dir):
            print()
            size_mb = calculate_package_size(dist_dir)
            print()
            
            zip_file = create_zip_package(dist_dir)
            print()
            
            print("=" * 60)
            print("DISTRIBUTION PACKAGE CREATED SUCCESSFULLY!")
            print("=" * 60)
            print(f"Distribution folder: {dist_dir}/")
            print(f"ZIP package: {zip_file}")
            print(f"Package size: {size_mb:.1f} MB")
            print()
            print("Ready for itch.io upload!")
            print("=" * 60)
            return 0
        else:
            print("ERROR: Distribution verification failed!")
            return 1
            
    except Exception as e:
        print(f"ERROR: Failed to create distribution: {e}")
        return 1

if __name__ == '__main__':
    exit_code = main()
    sys.exit(exit_code)
