<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Roguelike - Web Version</title>
    <meta name="description" content="A comprehensive roguelike game with character progression, multiple enemy types, and procedural level generation. Play directly in your browser!">
    <meta name="keywords" content="roguelike, game, python, pygame, web, browser, rpg, dungeon crawler">
    <meta name="author" content="sb (author), <PERSON><PERSON><PERSON><PERSON> (prompter/designer)">
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:title" content="Simple Roguelike - Web Version">
    <meta property="og:description" content="A comprehensive roguelike game with character progression, multiple enemy types, and procedural level generation.">
    
    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:title" content="Simple Roguelike - Web Version">
    <meta property="twitter:description" content="A comprehensive roguelike game with character progression, multiple enemy types, and procedural level generation.">
    
    <style>
        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            color: #ffffff;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
        }

        .game-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            max-width: 100vw;
            max-height: 100vh;
        }

        .game-header {
            text-align: center;
            margin-bottom: 10px;
            padding: 10px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            backdrop-filter: blur(10px);
        }

        .game-title {
            font-size: 2.5em;
            font-weight: bold;
            margin: 0;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
            background: linear-gradient(45deg, #ffd700, #ffed4e);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .game-subtitle {
            font-size: 1.2em;
            margin: 5px 0;
            opacity: 0.9;
        }

        #canvas {
            border: 3px solid #ffffff;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
            background: #000000;
            max-width: 100vw;
            max-height: 80vh;
        }

        .controls-info {
            margin-top: 15px;
            padding: 15px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            backdrop-filter: blur(10px);
            text-align: center;
            max-width: 800px;
        }

        .controls-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin-top: 10px;
        }

        .control-group {
            background: rgba(255, 255, 255, 0.1);
            padding: 10px;
            border-radius: 5px;
        }

        .control-group h4 {
            margin: 0 0 8px 0;
            color: #ffd700;
        }

        .control-group p {
            margin: 3px 0;
            font-size: 0.9em;
        }

        .loading-screen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }

        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 5px solid rgba(255, 255, 255, 0.3);
            border-top: 5px solid #ffd700;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .loading-text {
            font-size: 1.5em;
            margin-bottom: 10px;
        }

        .loading-progress {
            font-size: 1em;
            opacity: 0.8;
        }

        @media (max-width: 768px) {
            .game-title {
                font-size: 2em;
            }
            
            .game-subtitle {
                font-size: 1em;
            }
            
            .controls-grid {
                grid-template-columns: 1fr;
            }
            
            .controls-info {
                margin-top: 10px;
                padding: 10px;
            }
        }

        .footer-info {
            margin-top: 10px;
            text-align: center;
            font-size: 0.8em;
            opacity: 0.7;
        }
    </style>
</head>
<body>
    <!-- Loading Screen -->
    <div id="loading-screen" class="loading-screen">
        <div class="loading-spinner"></div>
        <div class="loading-text">Loading Simple Roguelike...</div>
        <div class="loading-progress">Initializing game systems...</div>
    </div>

    <!-- Main Game Container -->
    <div class="game-container">
        <div class="game-header">
            <h1 class="game-title">Simple Roguelike</h1>
            <p class="game-subtitle">Web Version - Play directly in your browser!</p>
        </div>

        <canvas id="canvas"></canvas>

        <div class="controls-info">
            <h3>🎮 Game Controls</h3>
            <div class="controls-grid">
                <div class="control-group">
                    <h4>Movement</h4>
                    <p>WASD or Arrow Keys</p>
                </div>
                <div class="control-group">
                    <h4>Combat</h4>
                    <p>Left Click or Space - Shoot</p>
                    <p>Mouse Wheel - Zoom</p>
                </div>
                <div class="control-group">
                    <h4>Menus</h4>
                    <p>I - Inventory</p>
                    <p>K - Skills</p>
                    <p>O - Achievements</p>
                    <p>C - Character Stats</p>
                </div>
                <div class="control-group">
                    <h4>Game</h4>
                    <p>P or ESC - Pause</p>
                    <p>U - Upgrades (when available)</p>
                </div>
            </div>
        </div>

        <div class="footer-info">
            <p>Created by sb (author) & Jozzpoly (prompter/designer) | Powered by Pygame & pygbag</p>
        </div>
    </div>

    <script>
        // Hide loading screen when game is ready
        function hideLoadingScreen() {
            const loadingScreen = document.getElementById('loading-screen');
            if (loadingScreen) {
                loadingScreen.style.opacity = '0';
                setTimeout(() => {
                    loadingScreen.style.display = 'none';
                }, 500);
            }
        }

        // Show loading progress
        function updateLoadingProgress(message) {
            const progressElement = document.querySelector('.loading-progress');
            if (progressElement) {
                progressElement.textContent = message;
            }
        }

        // Game initialization
        window.addEventListener('load', () => {
            updateLoadingProgress('Loading game assets...');
            
            // Hide loading screen after a delay to ensure game is ready
            setTimeout(() => {
                updateLoadingProgress('Starting game...');
                setTimeout(hideLoadingScreen, 1000);
            }, 2000);
        });

        // Prevent context menu on right click
        document.addEventListener('contextmenu', (e) => {
            e.preventDefault();
        });

        // Prevent default touch behaviors that might interfere with game
        document.addEventListener('touchstart', (e) => {
            if (e.target.tagName === 'CANVAS') {
                e.preventDefault();
            }
        }, { passive: false });

        document.addEventListener('touchmove', (e) => {
            if (e.target.tagName === 'CANVAS') {
                e.preventDefault();
            }
        }, { passive: false });
    </script>
</body>
</html>
