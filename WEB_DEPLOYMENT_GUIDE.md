# Simple Roguelike - Web Deployment Guide

This guide explains how to convert and deploy the Simple Roguelike game for web browsers using pygbag.

## Overview

The web version maintains all game features while being optimized for browser gameplay:
- ✅ Character progression system (XP, skills, equipment)
- ✅ All enemy types and AI behaviors  
- ✅ Procedural level generation
- ✅ Save/load functionality (using browser localStorage)
- ✅ All UI elements and keyboard controls
- ✅ 60 FPS performance optimization
- ✅ Cross-browser compatibility

## Prerequisites

1. **Python 3.8+** with pygame and numpy installed
2. **pygbag** for web conversion:
   ```bash
   pip install pygbag>=0.9.0
   ```

## Quick Start

### Option 1: Automated Build (Recommended)

1. Run the build script:
   ```bash
   python build_web.py
   ```

2. Follow the prompts to build and test

3. The script will create a `web_build` directory with all necessary files

### Option 2: Manual Build

1. Install pygbag:
   ```bash
   pip install pygbag
   ```

2. Create a build directory and copy files:
   ```bash
   mkdir web_build
   cp main_web.py game_web.py config.py web_build/
   cp -r utils entities level progression systems ui assets web_build/
   ```

3. Create main.py entry point in web_build/:
   ```python
   from main_web import main
   import asyncio
   
   if __name__ == "__main__":
       asyncio.run(main())
   ```

4. Run pygbag:
   ```bash
   cd web_build
   pygbag --app_name "Simple_Roguelike" --title "Simple Roguelike" --archive .
   ```

## Key Changes for Web Compatibility

### 1. Async Game Loop
- Converted synchronous game loop to async/await pattern
- Added `await asyncio.sleep(0)` in main loop for browser compatibility

### 2. Web Save System
- Replaced file-based saves with localStorage
- Maintains full save compatibility with all game features
- Automatic fallback for desktop testing

### 3. Browser Optimizations
- Disabled fullscreen toggle (browser limitation)
- Optimized for standard web resolutions (1280x720)
- Enhanced error handling for web environment

### 4. Asset Management
- All assets automatically bundled by pygbag
- Optimized loading for web performance
- Maintains full visual fidelity

## Testing Locally

1. After building, pygbag will start a local server
2. Navigate to `http://localhost:8000`
3. Test all game features:
   - Character movement and combat
   - Menu navigation (I, K, O, C keys)
   - Save/load functionality
   - Level progression
   - Equipment and skills

## Deploying to itch.io

### Step 1: Prepare Files
After running pygbag with `--archive` flag, you'll have a `web.zip` file containing:
- `index.html` (main page)
- `main.js` (game engine)
- `main.wasm` (compiled game)
- `assets/` (game assets)

### Step 2: Upload to itch.io
1. Go to [itch.io](https://itch.io) and create/login to your account
2. Click "Upload new project"
3. Fill in project details:
   - **Title**: Simple Roguelike
   - **Project URL**: choose your URL
   - **Classification**: Game
   - **Kind of project**: HTML

4. Upload the `web.zip` file
5. Set **"This file will be played in the browser"**
6. Configure viewport:
   - **Width**: 1280
   - **Height**: 720
   - **Fullscreen button**: Enable
   - **Mobile friendly**: Enable

### Step 3: Configure Game Page
1. Add description highlighting features:
   ```
   A comprehensive roguelike with character progression, multiple enemy types, 
   and procedural level generation. Features include:
   
   • Deep character progression with skills and equipment
   • 8+ unique enemy types with advanced AI
   • Procedural level generation
   • Achievement system
   • Save/load functionality
   • Optimized for 60 FPS gameplay
   ```

2. Add screenshots from the game
3. Set appropriate tags: `roguelike`, `rpg`, `action`, `browser`, `pixel-art`
4. Choose pricing (free recommended for initial release)

### Step 4: Test and Publish
1. Use itch.io's preview feature to test the game
2. Verify all controls work properly
3. Test save/load functionality
4. Publish when satisfied

## Controls for Web Players

| Action | Controls |
|--------|----------|
| Movement | WASD or Arrow Keys |
| Shoot | Left Click or Spacebar |
| Zoom | Mouse Wheel |
| Inventory | I key |
| Skills | K key |
| Achievements | O key |
| Character Stats | C key |
| Pause | P or ESC |
| Upgrades | U key (when available) |

## Performance Optimization

The web version includes several optimizations:

1. **Fixed 60 FPS** target for consistent performance
2. **Reduced enemy count** for web performance
3. **Optimized asset loading** through pygbag
4. **Efficient localStorage** for saves
5. **Browser-specific optimizations**

## Troubleshooting

### Common Issues

**Game doesn't load:**
- Check browser console for errors
- Ensure all assets are properly bundled
- Try a different browser (Chrome/Firefox recommended)

**Save/load not working:**
- Check if localStorage is enabled in browser
- Clear browser cache and try again
- Verify web_save_manager.py is included

**Performance issues:**
- Reduce browser zoom level
- Close other browser tabs
- Check if hardware acceleration is enabled

**Controls not responding:**
- Click on the game canvas to focus
- Check if browser is blocking input events
- Try refreshing the page

### Browser Compatibility

**Recommended browsers:**
- Chrome 81+
- Firefox 75+
- Safari 14+
- Edge 81+

**Mobile support:**
- iOS Safari 15+
- Chrome Mobile 81+
- Limited touch controls

## File Structure

```
web_build/
├── main.py              # Entry point for pygbag
├── main_web.py          # Web-compatible main loop
├── game_web.py          # Web-compatible game class
├── config.py            # Game configuration
├── utils/
│   ├── web_save_manager.py  # localStorage save system
│   └── ...              # Other utilities
├── entities/            # Game entities
├── level/              # Level generation
├── progression/        # Character progression
├── systems/            # Game systems
├── ui/                 # User interface
├── assets/             # Game assets
└── index.html          # Custom HTML template
```

## Credits

- **Author**: sb
- **Prompter/Designer**: Jozzpoly  
- **Engine**: Pygame + pygbag
- **Deployment**: Web-optimized for itch.io

## Support

For issues with the web version:
1. Check this guide first
2. Test the desktop version to isolate web-specific issues
3. Check browser console for error messages
4. Verify pygbag version compatibility

## Next Steps

After successful deployment:
1. Gather player feedback
2. Monitor performance metrics
3. Consider mobile-specific optimizations
4. Plan updates and new features

The web version maintains full feature parity with the desktop version while being optimized for browser gameplay. Players can enjoy the complete roguelike experience directly in their web browser!
