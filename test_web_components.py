#!/usr/bin/env python3
"""
Test script for web components
Verifies that web-specific components work correctly before deployment
"""

import sys
import logging
import traceback
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_imports():
    """Test that all required modules can be imported"""
    logger.info("Testing imports...")

    try:
        # Test web-specific imports
        from utils.web_save_manager import WebSaveManager
        logger.info("[OK] WebSaveManager import successful")

        from game_web import GameWeb
        logger.info("[OK] GameWeb import successful")

        # Test standard imports
        import pygame
        logger.info("[OK] Pygame import successful")

        import numpy
        logger.info("[OK] Numpy import successful")

        # Test game modules
        import utils.constants
        from config import PLAYER_BASE_SPEED
        from entities.player import Player
        from level.level import Level
        logger.info("[OK] Game modules import successful")

        return True

    except ImportError as e:
        logger.error(f"❌ Import failed: {e}")
        return False
    except Exception as e:
        logger.error(f"❌ Unexpected error during import: {e}")
        return False

def test_web_save_manager():
    """Test the web save manager functionality"""
    logger.info("Testing WebSaveManager...")

    try:
        from utils.web_save_manager import WebSaveManager

        # Create save manager
        save_manager = WebSaveManager("test_save")

        # Test save data
        test_data = {
            "current_level": 5,
            "score": 1000,
            "high_score": 2000,
            "player_data": {
                "health": 100,
                "max_health": 120,
                "damage": 15,
                "speed": 3.0,
                "fire_rate": 400,
                "level": 5,
                "xp": 500,
                "xp_to_next_level": 600,
                "upgrade_points": 2
            }
        }

        # Test save
        if save_manager.save_game(test_data):
            logger.info("✅ Save operation successful")
        else:
            logger.error("❌ Save operation failed")
            return False

        # Test load
        loaded_data = save_manager.load_game()
        if loaded_data:
            logger.info("✅ Load operation successful")

            # Verify data integrity
            if loaded_data["current_level"] == test_data["current_level"]:
                logger.info("✅ Data integrity verified")
            else:
                logger.error("❌ Data integrity check failed")
                return False
        else:
            logger.error("❌ Load operation failed")
            return False

        # Test save exists
        if save_manager.save_exists():
            logger.info("✅ Save exists check successful")
        else:
            logger.error("❌ Save exists check failed")
            return False

        # Test delete
        if save_manager.delete_save():
            logger.info("✅ Delete operation successful")
        else:
            logger.error("❌ Delete operation failed")
            return False

        # Verify deletion
        if not save_manager.save_exists():
            logger.info("✅ Deletion verified")
        else:
            logger.error("❌ Deletion verification failed")
            return False

        return True

    except Exception as e:
        logger.error(f"❌ WebSaveManager test failed: {e}")
        traceback.print_exc()
        return False

def test_game_web_initialization():
    """Test GameWeb class initialization"""
    logger.info("Testing GameWeb initialization...")

    try:
        # Initialize pygame (required for GameWeb)
        import pygame
        pygame.init()

        from game_web import GameWeb

        # Create game instance
        game = GameWeb()
        logger.info("✅ GameWeb initialization successful")

        # Test basic properties
        if hasattr(game, 'save_manager'):
            logger.info("✅ WebSaveManager integration successful")
        else:
            logger.error("❌ WebSaveManager integration failed")
            return False

        if hasattr(game, 'running'):
            logger.info("✅ Game state properties present")
        else:
            logger.error("❌ Game state properties missing")
            return False

        # Test web-specific features
        if game.windowed_size == (1280, 720):
            logger.info("✅ Web-optimized resolution set")
        else:
            logger.warning(f"⚠️  Unexpected resolution: {game.windowed_size}")

        # Clean up
        pygame.quit()

        return True

    except Exception as e:
        logger.error(f"❌ GameWeb initialization failed: {e}")
        traceback.print_exc()
        return False

def test_async_compatibility():
    """Test async/await compatibility"""
    logger.info("Testing async compatibility...")

    try:
        import asyncio

        async def test_async_function():
            """Test async function"""
            await asyncio.sleep(0)
            return True

        # Run async test
        result = asyncio.run(test_async_function())

        if result:
            logger.info("✅ Async/await compatibility verified")
            return True
        else:
            logger.error("❌ Async/await test failed")
            return False

    except Exception as e:
        logger.error(f"❌ Async compatibility test failed: {e}")
        return False

def test_file_structure():
    """Test that all required files exist"""
    logger.info("Testing file structure...")

    required_files = [
        "main_web.py",
        "game_web.py",
        "utils/web_save_manager.py",
        "web_template.html",
        "build_web.py",
        "config.py"
    ]

    required_dirs = [
        "utils",
        "entities",
        "level",
        "progression",
        "systems",
        "ui",
        "assets"
    ]

    project_root = Path(__file__).parent

    # Check files
    for file_path in required_files:
        full_path = project_root / file_path
        if full_path.exists():
            logger.info(f"✅ Found {file_path}")
        else:
            logger.error(f"❌ Missing {file_path}")
            return False

    # Check directories
    for dir_path in required_dirs:
        full_path = project_root / dir_path
        if full_path.exists() and full_path.is_dir():
            logger.info(f"✅ Found directory {dir_path}")
        else:
            logger.error(f"❌ Missing directory {dir_path}")
            return False

    return True

def main():
    """Run all tests"""
    print("Simple Roguelike - Web Components Test")
    print("=" * 50)

    tests = [
        ("File Structure", test_file_structure),
        ("Imports", test_imports),
        ("WebSaveManager", test_web_save_manager),
        ("GameWeb Initialization", test_game_web_initialization),
        ("Async Compatibility", test_async_compatibility)
    ]

    results = []

    for test_name, test_func in tests:
        print(f"\n[TEST] Running {test_name} test...")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"[PASS] {test_name} test PASSED")
            else:
                print(f"[FAIL] {test_name} test FAILED")
        except Exception as e:
            print(f"[CRASH] {test_name} test CRASHED: {e}")
            results.append((test_name, False))

    # Summary
    print("\n" + "=" * 50)
    print("TEST SUMMARY")
    print("=" * 50)

    passed = sum(1 for _, result in results if result)
    total = len(results)

    for test_name, result in results:
        status = "[PASS]" if result else "[FAIL]"
        print(f"{test_name:.<30} {status}")

    print(f"\nOverall: {passed}/{total} tests passed")

    if passed == total:
        print("\n[SUCCESS] All tests passed! Web components are ready for deployment.")
        return True
    else:
        print(f"\n[WARNING] {total - passed} test(s) failed. Please fix issues before deployment.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
