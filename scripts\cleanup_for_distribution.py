#!/usr/bin/env python3
"""
Distribution cleanup script for Simple Roguelike Game.

This script prepares the project for distribution by:
1. Removing development artifacts (__pycache__, .pyc files)
2. Cleaning up temporary files
3. Organizing assets properly
4. Creating a clean distribution structure
"""

import os
import shutil
import glob
import sys
from pathlib import Path

def remove_pycache_directories():
    """Remove all __pycache__ directories recursively."""
    print("Removing __pycache__ directories...")
    pycache_dirs = []

    for root, dirs, files in os.walk('.'):
        if '__pycache__' in dirs:
            pycache_path = os.path.join(root, '__pycache__')
            pycache_dirs.append(pycache_path)

    for pycache_dir in pycache_dirs:
        try:
            shutil.rmtree(pycache_dir)
            print(f"  Removed: {pycache_dir}")
        except Exception as e:
            print(f"  Error removing {pycache_dir}: {e}")

    print(f"Removed {len(pycache_dirs)} __pycache__ directories.")

def remove_pyc_files():
    """Remove all .pyc files recursively."""
    print("Removing .pyc files...")
    pyc_files = glob.glob('**/*.pyc', recursive=True)

    for pyc_file in pyc_files:
        try:
            os.remove(pyc_file)
            print(f"  Removed: {pyc_file}")
        except Exception as e:
            print(f"  Error removing {pyc_file}: {e}")

    print(f"Removed {len(pyc_files)} .pyc files.")

def remove_temp_files():
    """Remove temporary and log files."""
    print("Removing temporary files...")
    temp_patterns = [
        '**/*.tmp',
        '**/*.log',
        '**/*.bak',
        '**/*~',
        '**/Thumbs.db',
        '**/.DS_Store'
    ]

    removed_count = 0
    for pattern in temp_patterns:
        temp_files = glob.glob(pattern, recursive=True)
        for temp_file in temp_files:
            try:
                os.remove(temp_file)
                print(f"  Removed: {temp_file}")
                removed_count += 1
            except Exception as e:
                print(f"  Error removing {temp_file}: {e}")

    print(f"Removed {removed_count} temporary files.")

def verify_essential_files():
    """Verify that essential files are present."""
    print("Verifying essential files...")
    essential_files = [
        'main.py',
        'game.py',
        'config.py',
        'requirements.txt',
        'README.md'
    ]

    missing_files = []
    for file in essential_files:
        if not os.path.exists(file):
            missing_files.append(file)
        else:
            print(f"  [OK] {file}")

    if missing_files:
        print(f"WARNING: Missing essential files: {missing_files}")
        return False
    else:
        print("All essential files present.")
        return True

def verify_directory_structure():
    """Verify that the expected directory structure exists."""
    print("Verifying directory structure...")
    expected_dirs = [
        'assets',
        'docs',
        'tests',
        'entities',
        'level',
        'progression',
        'systems',
        'ui',
        'utils',
        'scripts'
    ]

    missing_dirs = []
    for dir_name in expected_dirs:
        if not os.path.isdir(dir_name):
            missing_dirs.append(dir_name)
        else:
            print(f"  [OK] {dir_name}/")

    if missing_dirs:
        print(f"WARNING: Missing directories: {missing_dirs}")
        return False
    else:
        print("All expected directories present.")
        return True

def create_distribution_info():
    """Create distribution information file."""
    print("Creating distribution info...")

    dist_info = """# Simple Roguelike - Distribution Information

## Version Information
- Game Version: 4.0 (Launch Ready)
- Build Date: Generated automatically
- Python Version: 3.7+
- Pygame Version: 2.0.0+

## Distribution Contents
- Main game files (main.py, game.py, config.py)
- Game modules (entities/, level/, progression/, systems/, ui/, utils/)
- Assets (assets/)
- Documentation (docs/)
- Tests (tests/)
- Scripts (scripts/)

## Installation
1. Ensure Python 3.7+ is installed
2. Install dependencies: pip install -r requirements.txt
3. Run the game: python main.py

## Attribution
- Author: sb (System Builder/Developer)
- Prompter/Designer: Jozzpoly (Game Design & Direction)
- Engine: Python 3.x with Pygame 2.x

## License
See LICENSE file for details.
"""

    try:
        with open('DISTRIBUTION_INFO.md', 'w', encoding='utf-8') as f:
            f.write(dist_info)
        print("  Created: DISTRIBUTION_INFO.md")
    except Exception as e:
        print(f"  Error creating distribution info: {e}")

def main():
    """Main cleanup function."""
    print("=" * 60)
    print("SIMPLE ROGUELIKE - DISTRIBUTION CLEANUP")
    print("=" * 60)

    # Change to project root directory
    script_dir = os.path.dirname(os.path.abspath(__file__))
    project_root = os.path.dirname(script_dir)
    os.chdir(project_root)

    print(f"Working directory: {os.getcwd()}")
    print()

    # Perform cleanup operations
    remove_pycache_directories()
    print()

    remove_pyc_files()
    print()

    remove_temp_files()
    print()

    # Verify project integrity
    files_ok = verify_essential_files()
    print()

    dirs_ok = verify_directory_structure()
    print()

    # Create distribution info
    create_distribution_info()
    print()

    # Final status
    if files_ok and dirs_ok:
        print("=" * 60)
        print("CLEANUP COMPLETED SUCCESSFULLY!")
        print("Project is ready for distribution.")
        print("=" * 60)
        return 0
    else:
        print("=" * 60)
        print("CLEANUP COMPLETED WITH WARNINGS!")
        print("Please review the warnings above.")
        print("=" * 60)
        return 1

if __name__ == '__main__':
    exit_code = main()
    sys.exit(exit_code)
