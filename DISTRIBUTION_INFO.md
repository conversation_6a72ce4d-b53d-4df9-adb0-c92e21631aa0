# Simple Roguelike - Distribution Information

## Version Information
- Game Version: 4.0 (Launch Ready)
- Build Date: Generated automatically
- Python Version: 3.7+
- Pygame Version: 2.0.0+

## Distribution Contents
- Main game files (main.py, game.py, config.py)
- Game modules (entities/, level/, progression/, systems/, ui/, utils/)
- Assets (assets/)
- Documentation (docs/)
- Tests (tests/)
- Scripts (scripts/)

## Installation
1. Ensure Python 3.7+ is installed
2. Install dependencies: pip install -r requirements.txt
3. Run the game: python main.py

## Attribution
- Author: sb (System Builder/Developer)
- Prompter/Designer: <PERSON><PERSON><PERSON><PERSON> (Game Design & Direction)
- Engine: Python 3.x with Pygame 2.x

## License
See LICENSE file for details.
