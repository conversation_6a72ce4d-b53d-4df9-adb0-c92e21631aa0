MIT License

Copyright (c) 2024 Simple Roguelike Game

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.

================================================================================
ATTRIBUTION AND CREDITS
================================================================================

This Simple Roguelike Game was developed through collaborative effort:

PRIMARY CONTRIBUTORS:
- Author/Developer: sb (System Builder/Developer)
  - Responsible for: Code implementation, system architecture, technical design
  
- Prompter/Designer: Jozzpoly (Game Design & Direction)  
  - Responsible for: Game design, feature specification, creative direction

TECHNICAL STACK:
- Engine: Python 3.x with Pygame 2.x
- Architecture: Modular object-oriented design with type annotations
- Development Approach: Iterative development through 4 major phases

DEVELOPMENT PHASES:
- Phase 1: Core mechanics and basic systems foundation
- Phase 2: Enhanced enemies, elemental combat, equipment system  
- Phase 3: Meta-progression, dynamic difficulty, advanced generation
- Phase 4: Polish, integration, settings, tutorials, launch preparation

THIRD-PARTY DEPENDENCIES:
- Pygame (https://pygame.org) - Game development framework
- NumPy (https://numpy.org) - Numerical computing library

SPECIAL ACKNOWLEDGMENTS:
- The Pygame community for excellent documentation and support
- The Python community for providing a robust development ecosystem

================================================================================
USAGE TERMS
================================================================================

This game is provided under the MIT License for educational and entertainment 
purposes. You are free to:

- Play and enjoy the game
- Study the source code for learning purposes
- Modify the game for personal use
- Distribute modified versions (with proper attribution)
- Use portions of the code in your own projects (with attribution)

ATTRIBUTION REQUIREMENTS:
When using this code or distributing modified versions, please maintain:
- This license file
- Attribution to original contributors (sb and Jozzpoly)
- Reference to the original project

DISCLAIMER:
This software is provided "as is" without warranty of any kind. The authors
are not responsible for any issues that may arise from using this software.

For questions about licensing or attribution, please refer to the project
documentation or contact the contributors through appropriate channels.
