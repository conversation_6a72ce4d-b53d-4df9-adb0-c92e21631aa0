#!/usr/bin/env python3
"""
Publication readiness verification script for Simple Roguelike Game.

This script performs comprehensive checks to ensure the project is ready for itch.io publication.
"""

import os
import sys
import subprocess
import zipfile
from pathlib import Path

def check_essential_files():
    """Check that all essential files are present."""
    print("[CHECK] Checking essential files...")

    essential_files = [
        'main.py',
        'game.py',
        'config.py',
        'requirements.txt',
        'README.md',
        'LICENSE',
        'CHANGELOG.md',
        'ITCH_IO_SETUP.md',
        'ITCH_IO_PUBLICATION_READY.md',
        'DISTRIBUTION_INFO.md'
    ]

    missing_files = []
    for file in essential_files:
        if os.path.exists(file):
            print(f"  [OK] {file}")
        else:
            print(f"  [MISSING] {file}")
            missing_files.append(file)

    return len(missing_files) == 0

def check_directory_structure():
    """Check that all required directories exist."""
    print("\n[CHECK] Checking directory structure...")

    required_dirs = [
        'assets',
        'docs',
        'entities',
        'level',
        'progression',
        'systems',
        'ui',
        'utils',
        'scripts',
        'tests'
    ]

    missing_dirs = []
    for directory in required_dirs:
        if os.path.isdir(directory):
            print(f"  [OK] {directory}/")
        else:
            print(f"  [MISSING] {directory}/")
            missing_dirs.append(directory)

    return len(missing_dirs) == 0

def check_distribution_package():
    """Check if distribution package exists and is valid."""
    print("\n[CHECK] Checking distribution package...")

    # Look for ZIP files
    zip_files = [f for f in os.listdir('.') if f.startswith('simple_roguelike_v4.0_') and f.endswith('.zip')]

    if not zip_files:
        print("  [MISSING] No distribution ZIP file found")
        return False

    latest_zip = max(zip_files, key=os.path.getmtime)
    print(f"  [OK] Found distribution package: {latest_zip}")

    # Check ZIP contents
    try:
        with zipfile.ZipFile(latest_zip, 'r') as zipf:
            files = zipf.namelist()
            essential_in_zip = ['main.py', 'requirements.txt', 'README.md']

            for file in essential_in_zip:
                if file in files:
                    print(f"    [OK] {file} in ZIP")
                else:
                    print(f"    [MISSING] {file} missing from ZIP")
                    return False

        # Check ZIP size
        zip_size = os.path.getsize(latest_zip) / (1024 * 1024)  # MB
        print(f"  [OK] Package size: {zip_size:.1f} MB")

        return True

    except Exception as e:
        print(f"  [ERROR] Error checking ZIP file: {e}")
        return False

def check_dist_directory():
    """Check if dist directory exists and is valid."""
    print("\n[CHECK] Checking dist directory...")

    if not os.path.isdir('dist'):
        print("  [MISSING] dist/ directory not found")
        return False

    print("  [OK] dist/ directory exists")

    # Check essential files in dist
    essential_files = ['main.py', 'requirements.txt', 'README.md', 'QUICK_START.md']
    for file in essential_files:
        dist_file = os.path.join('dist', file)
        if os.path.exists(dist_file):
            print(f"    [OK] {file}")
        else:
            print(f"    [MISSING] {file}")
            return False

    return True

def run_tests():
    """Run the test suite to verify functionality."""
    print("\n[CHECK] Running test suite...")

    try:
        result = subprocess.run([sys.executable, 'run_tests.py'],
                              capture_output=True, text=True, timeout=60)

        if result.returncode == 0:
            print("  [OK] All tests passed")
            return True
        else:
            print("  [FAIL] Some tests failed")
            print(f"    Error output: {result.stderr}")
            return False

    except subprocess.TimeoutExpired:
        print("  [FAIL] Tests timed out")
        return False
    except Exception as e:
        print(f"  [FAIL] Error running tests: {e}")
        return False

def test_game_launch():
    """Test that the game can launch successfully."""
    print("\n[CHECK] Testing game launch...")

    try:
        # Check if main.py exists and is executable
        if not os.path.exists('main.py'):
            print("  [FAIL] main.py not found")
            return False

        # Check if game.py exists
        if not os.path.exists('game.py'):
            print("  [FAIL] game.py not found")
            return False

        print("  [OK] Essential game files present")

        # Try a quick syntax check by compiling the main file
        try:
            with open('main.py', 'r', encoding='utf-8') as f:
                code = f.read()
            compile(code, 'main.py', 'exec')
            print("  [OK] main.py syntax is valid")
        except SyntaxError as e:
            print(f"  [FAIL] main.py has syntax errors: {e}")
            return False

        print("  [OK] Game launch test passed (syntax validation)")
        return True

    except Exception as e:
        print(f"  [FAIL] Game launch test failed: {e}")
        return False

def check_documentation():
    """Check that documentation is complete."""
    print("\n[CHECK] Checking documentation...")

    doc_files = [
        'README.md',
        'CHANGELOG.md',
        'LICENSE',
        'ITCH_IO_SETUP.md',
        'ITCH_IO_PUBLICATION_READY.md'
    ]

    all_present = True
    for doc in doc_files:
        if os.path.exists(doc):
            # Check file size to ensure it's not empty
            size = os.path.getsize(doc)
            if size > 100:  # At least 100 bytes
                print(f"  [OK] {doc} ({size} bytes)")
            else:
                print(f"  [WARNING] {doc} seems too small ({size} bytes)")
                all_present = False
        else:
            print(f"  [MISSING] {doc}")
            all_present = False

    return all_present

def check_attribution():
    """Check that proper attribution is included."""
    print("\n[CHECK] Checking attribution...")

    attribution_found = False

    # Check README.md for attribution
    try:
        with open('README.md', 'r', encoding='utf-8') as f:
            content = f.read()
            if 'sb' in content and 'Jozzpoly' in content:
                print("  [OK] Attribution found in README.md")
                attribution_found = True
            else:
                print("  [MISSING] Attribution missing from README.md")
    except:
        print("  [ERROR] Could not check README.md")

    # Check LICENSE file
    try:
        with open('LICENSE', 'r', encoding='utf-8') as f:
            content = f.read()
            if 'sb' in content and 'Jozzpoly' in content:
                print("  [OK] Attribution found in LICENSE")
                attribution_found = True
    except:
        print("  [ERROR] Could not check LICENSE")

    return attribution_found

def main():
    """Main verification function."""
    print("=" * 60)
    print("SIMPLE ROGUELIKE - PUBLICATION READINESS CHECK")
    print("=" * 60)

    checks = [
        ("Essential Files", check_essential_files),
        ("Directory Structure", check_directory_structure),
        ("Distribution Package", check_distribution_package),
        ("Dist Directory", check_dist_directory),
        ("Test Suite", run_tests),
        ("Game Launch", test_game_launch),
        ("Documentation", check_documentation),
        ("Attribution", check_attribution)
    ]

    passed_checks = 0
    total_checks = len(checks)

    for check_name, check_func in checks:
        try:
            if check_func():
                passed_checks += 1
            else:
                print(f"\n[FAIL] {check_name} check failed!")
        except Exception as e:
            print(f"\n[CRASH] {check_name} check crashed: {e}")

    print("\n" + "=" * 60)
    print("VERIFICATION RESULTS")
    print("=" * 60)
    print(f"Checks passed: {passed_checks}/{total_checks}")

    if passed_checks == total_checks:
        print("\n[SUCCESS] ALL CHECKS PASSED!")
        print("[READY] Project is ready for itch.io publication!")
        print("\nNext steps:")
        print("1. Upload the ZIP file to itch.io")
        print("2. Add screenshots and media")
        print("3. Configure listing details")
        print("4. Publish and share!")
        return 0
    else:
        print(f"\n[WARNING] {total_checks - passed_checks} checks failed.")
        print("[FAIL] Please address the issues above before publication.")
        return 1

if __name__ == '__main__':
    exit_code = main()
    sys.exit(exit_code)
