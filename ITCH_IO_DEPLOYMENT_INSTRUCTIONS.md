# Simple Roguelike - itch.io Deployment Instructions

Complete step-by-step guide for deploying the Simple Roguelike game to itch.io.

## Pre-Deployment Checklist

### 1. Test Web Components
```bash
python test_web_components.py
```
Ensure all tests pass before proceeding.

### 2. Install Dependencies
```bash
pip install pygbag>=0.9.0
```

### 3. Build Web Version
```bash
python build_web.py
```
Follow prompts to build and test locally.

## Step-by-Step itch.io Deployment

### Step 1: Prepare Build Files

1. **Run the build script:**
   ```bash
   python build_web.py
   ```

2. **Verify build output:**
   - Check `web_build/` directory exists
   - Confirm `main.py`, `game_web.py`, and assets are present
   - Test locally at `http://localhost:8000`

3. **Generate deployment package:**
   ```bash
   cd web_build
   pygbag --archive --app_name "Simple_Roguelike" --title "Simple Roguelike" .
   ```

### Step 2: Create itch.io Project

1. **Go to itch.io:**
   - Visit [itch.io](https://itch.io)
   - Login or create account
   - Click "Upload new project"

2. **Basic Project Setup:**
   - **Title:** Simple Roguelike
   - **Project URL:** `your-username.itch.io/simple-roguelike`
   - **Short description:** "A comprehensive roguelike with character progression and procedural generation"
   - **Classification:** Game
   - **Kind of project:** HTML

### Step 3: Upload Game Files

1. **Upload the web.zip file:**
   - Drag and drop `web.zip` from your build directory
   - Check "This file will be played in the browser"

2. **Configure viewport:**
   - **Width:** 1280
   - **Height:** 720
   - **Fullscreen button:** ✅ Enable
   - **Mobile friendly:** ✅ Enable
   - **Automatically start on page load:** ✅ Enable

### Step 4: Game Page Configuration

#### Description Template:
```markdown
# Simple Roguelike

A comprehensive roguelike adventure featuring deep character progression, strategic combat, and endless replayability!

## 🎮 Key Features

**Character Progression:**
- Level up system with skill points
- Equipment system with set bonuses
- Achievement system with unlockables
- Persistent progression between runs

**Combat & Enemies:**
- 8+ unique enemy types with distinct AI behaviors
- Tactical combat with positioning and timing
- Special enemy abilities and coordinated attacks
- Boss encounters with unique mechanics

**World & Exploration:**
- Procedurally generated levels
- Progressive difficulty scaling
- Multiple biomes and environments
- Hidden secrets and treasure rooms

**Quality of Life:**
- Auto-save functionality
- Comprehensive UI with detailed stats
- Customizable controls
- Performance optimized for 60 FPS

## 🎯 Controls

| Action | Controls |
|--------|----------|
| Movement | WASD or Arrow Keys |
| Attack | Left Click or Spacebar |
| Zoom | Mouse Wheel |
| Inventory | I key |
| Skills | K key |
| Achievements | O key |
| Character Stats | C key |
| Pause | P or ESC |

## 🏆 Perfect For

- Fans of classic roguelikes
- Players who enjoy character progression
- Anyone looking for a challenging but fair experience
- Browser gaming enthusiasts

**Play directly in your browser - no downloads required!**

---
*Created by sb (author) & Jozzpoly (prompter/designer)*
```

#### Tags to Add:
- `roguelike`
- `rpg` 
- `action`
- `browser`
- `pixel-art`
- `dungeon-crawler`
- `character-progression`
- `procedural-generation`

#### Screenshots to Include:
1. Main gameplay showing player, enemies, and UI
2. Character progression screen (skills/equipment)
3. Level generation variety
4. Combat in action
5. Achievement/progression systems

### Step 5: Pricing and Access

**Recommended Settings:**
- **Pricing:** Free (for initial release)
- **Visibility:** Public
- **Community:** Enable comments and ratings
- **Donations:** Enable if desired

### Step 6: Advanced Settings

#### Metadata:
- **Genre:** Action, Role Playing
- **Made with:** Pygame, Python
- **Average session:** 15-30 minutes
- **Languages:** English
- **Accessibility:** Keyboard accessible
- **Multiplayer:** Single-player

#### SEO Optimization:
- **Search tags:** Include relevant keywords
- **Release status:** Released
- **Development status:** Complete

### Step 7: Final Testing

1. **Use itch.io preview:**
   - Click "Preview" before publishing
   - Test all game features
   - Verify controls work properly
   - Check save/load functionality

2. **Cross-browser testing:**
   - Test in Chrome, Firefox, Safari
   - Verify mobile compatibility
   - Check performance on different devices

3. **Functionality checklist:**
   - [ ] Game loads without errors
   - [ ] All controls responsive
   - [ ] Save/load works correctly
   - [ ] Audio plays properly
   - [ ] UI elements display correctly
   - [ ] Performance maintains 60 FPS

### Step 8: Publish

1. **Final review:**
   - Double-check all information
   - Verify screenshots and description
   - Confirm pricing and access settings

2. **Publish:**
   - Click "Save & view page"
   - Review published page
   - Share with friends for initial testing

## Post-Deployment

### Monitoring
- Check itch.io analytics regularly
- Monitor comments and feedback
- Track download/play statistics

### Updates
- Use same build process for updates
- Upload new web.zip files as needed
- Update version notes in description

### Promotion
- Share on social media
- Submit to game directories
- Engage with the itch.io community
- Consider participating in game jams

## Troubleshooting Common Issues

### Game Won't Load
- **Check file size:** Ensure web.zip is under itch.io limits
- **Verify build:** Test locally before uploading
- **Browser console:** Check for JavaScript errors

### Performance Issues
- **Reduce quality:** Consider asset optimization
- **Check viewport:** Ensure correct dimensions
- **Test browsers:** Some browsers perform better

### Save/Load Problems
- **localStorage:** Verify browser supports localStorage
- **Clear cache:** Test with fresh browser session
- **Error handling:** Check console for save errors

### Mobile Issues
- **Touch controls:** May need mobile-specific adaptations
- **Performance:** Mobile devices may struggle with complex scenes
- **Viewport:** Ensure mobile-friendly settings enabled

## Success Metrics

Track these metrics for success:
- **Views:** Page visits and game loads
- **Engagement:** Average session time
- **Retention:** Return players
- **Feedback:** Comments and ratings
- **Performance:** Technical metrics

## Support Resources

- **itch.io Creator Guide:** [itch.io/docs](https://itch.io/docs)
- **pygbag Documentation:** [pygame-web.github.io](https://pygame-web.github.io)
- **Community Forums:** itch.io community discussions

---

**Ready to deploy?** Follow this checklist step-by-step for a successful itch.io launch of your Simple Roguelike game!
