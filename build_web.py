#!/usr/bin/env python3
"""
Web build script for Simple Roguelike
This script prepares the game for web deployment using pygbag
"""

import os
import sys
import shutil
import subprocess
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class WebBuilder:
    """<PERSON>les building the game for web deployment"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.web_build_dir = self.project_root / "web_build"
        self.assets_dir = self.project_root / "assets"
        
    def clean_build_directory(self):
        """Clean the web build directory"""
        if self.web_build_dir.exists():
            logger.info("Cleaning existing web build directory...")
            shutil.rmtree(self.web_build_dir)
        
        self.web_build_dir.mkdir(exist_ok=True)
        logger.info(f"Created clean build directory: {self.web_build_dir}")
    
    def copy_game_files(self):
        """Copy necessary game files to build directory"""
        logger.info("Copying game files...")
        
        # Files to copy
        files_to_copy = [
            "main_web.py",
            "game_web.py",
            "config.py",
            "requirements.txt"
        ]
        
        # Directories to copy
        dirs_to_copy = [
            "utils",
            "entities", 
            "level",
            "progression",
            "systems",
            "ui",
            "assets"
        ]
        
        # Copy individual files
        for file_name in files_to_copy:
            src = self.project_root / file_name
            if src.exists():
                dst = self.web_build_dir / file_name
                shutil.copy2(src, dst)
                logger.info(f"Copied {file_name}")
            else:
                logger.warning(f"File not found: {file_name}")
        
        # Copy directories
        for dir_name in dirs_to_copy:
            src = self.project_root / dir_name
            if src.exists():
                dst = self.web_build_dir / dir_name
                shutil.copytree(src, dst)
                logger.info(f"Copied directory {dir_name}")
            else:
                logger.warning(f"Directory not found: {dir_name}")
    
    def create_main_py(self):
        """Create the main.py entry point for pygbag"""
        logger.info("Creating main.py entry point...")
        
        main_content = '''"""
Main entry point for web deployment.
This file is required by pygbag and must be named main.py
"""

# Import and run the web version
from main_web import main
import asyncio

if __name__ == "__main__":
    asyncio.run(main())
'''
        
        main_py_path = self.web_build_dir / "main.py"
        with open(main_py_path, 'w') as f:
            f.write(main_content)
        
        logger.info("Created main.py entry point")
    
    def copy_web_template(self):
        """Copy the custom HTML template"""
        logger.info("Copying web template...")
        
        src_template = self.project_root / "web_template.html"
        if src_template.exists():
            dst_template = self.web_build_dir / "index.html"
            shutil.copy2(src_template, dst_template)
            logger.info("Copied web template as index.html")
        else:
            logger.warning("Web template not found, pygbag will use default")
    
    def optimize_assets(self):
        """Optimize assets for web deployment"""
        logger.info("Optimizing assets for web...")
        
        # Check if assets directory exists in build
        assets_build_dir = self.web_build_dir / "assets"
        if not assets_build_dir.exists():
            logger.warning("No assets directory found in build")
            return
        
        # Count assets
        image_count = 0
        sound_count = 0
        
        for root, dirs, files in os.walk(assets_build_dir):
            for file in files:
                if file.lower().endswith(('.png', '.jpg', '.jpeg', '.gif', '.bmp')):
                    image_count += 1
                elif file.lower().endswith(('.wav', '.mp3', '.ogg')):
                    sound_count += 1
        
        logger.info(f"Found {image_count} image files and {sound_count} sound files")
        
        # Note: pygbag handles asset optimization automatically
        logger.info("Assets ready for web deployment")
    
    def create_build_info(self):
        """Create build information file"""
        logger.info("Creating build information...")
        
        build_info = f"""# Simple Roguelike - Web Build Information

## Build Details
- Built for web deployment using pygbag
- Entry point: main.py
- Template: index.html (custom)
- Assets: Optimized for web

## Deployment Instructions
1. Run: `pygbag .` in this directory
2. Navigate to http://localhost:8000
3. For itch.io: Use the generated web.zip file

## Controls
- WASD/Arrow Keys: Movement
- Left Click/Space: Shoot
- Mouse Wheel: Zoom
- I: Inventory
- K: Skills  
- O: Achievements
- C: Character Stats
- P/ESC: Pause

## Features
- Character progression system
- Multiple enemy types with AI
- Procedural level generation
- Equipment and skill systems
- Achievement system
- Save/load using localStorage

## Credits
- Author: sb
- Prompter/Designer: Jozzpoly
- Engine: Pygame + pygbag
"""
        
        readme_path = self.web_build_dir / "README.md"
        with open(readme_path, 'w') as f:
            f.write(build_info)
        
        logger.info("Created build information")
    
    def run_pygbag_build(self, test_server=True):
        """Run pygbag to build the web version"""
        logger.info("Running pygbag build...")
        
        # Change to build directory
        original_cwd = os.getcwd()
        os.chdir(self.web_build_dir)
        
        try:
            # Build command
            cmd = [
                sys.executable, "-m", "pygbag",
                "--app_name", "Simple_Roguelike",
                "--title", "Simple Roguelike - Web Version",
                "--template", "index.html",
                "--archive",  # Create zip for itch.io
                "--build"  # Build only, don't run server initially
            ]
            
            if not test_server:
                cmd.append("--build")
            
            cmd.append(".")  # Current directory
            
            logger.info(f"Running command: {' '.join(cmd)}")
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                logger.info("pygbag build completed successfully!")
                logger.info("Build output:")
                logger.info(result.stdout)
                
                if test_server:
                    logger.info("Test server should be running at http://localhost:8000")
                    logger.info("Press Ctrl+C to stop the server")
            else:
                logger.error("pygbag build failed!")
                logger.error("Error output:")
                logger.error(result.stderr)
                return False
                
        except FileNotFoundError:
            logger.error("pygbag not found! Please install with: pip install pygbag")
            return False
        except Exception as e:
            logger.error(f"Error running pygbag: {e}")
            return False
        finally:
            os.chdir(original_cwd)
        
        return True
    
    def build(self, test_server=True):
        """Complete build process"""
        logger.info("Starting web build process...")
        
        try:
            self.clean_build_directory()
            self.copy_game_files()
            self.create_main_py()
            self.copy_web_template()
            self.optimize_assets()
            self.create_build_info()
            
            logger.info("Build preparation completed successfully!")
            logger.info(f"Build directory: {self.web_build_dir}")
            
            # Ask user if they want to run pygbag
            if input("\nRun pygbag build now? (y/n): ").lower().startswith('y'):
                return self.run_pygbag_build(test_server)
            else:
                logger.info("Build prepared. Run 'pygbag .' in the web_build directory when ready.")
                return True
                
        except Exception as e:
            logger.error(f"Build failed: {e}")
            return False

def main():
    """Main build function"""
    print("Simple Roguelike - Web Build Script")
    print("=" * 40)
    
    builder = WebBuilder()
    
    # Check if pygbag is installed
    try:
        import pygbag
        logger.info(f"pygbag version found: {pygbag.__version__}")
    except ImportError:
        logger.warning("pygbag not installed. Install with: pip install pygbag")
        if input("Continue anyway? (y/n): ").lower().startswith('n'):
            return
    
    # Run build
    success = builder.build()
    
    if success:
        print("\n" + "=" * 40)
        print("✅ Web build completed successfully!")
        print(f"📁 Build directory: {builder.web_build_dir}")
        print("🌐 To deploy to itch.io, upload the generated web.zip file")
        print("🚀 To test locally, run 'pygbag .' in the build directory")
    else:
        print("\n" + "=" * 40)
        print("❌ Web build failed!")
        print("Check the logs above for details")

if __name__ == "__main__":
    main()
