"""
Web-compatible save manager using browser localStorage.
This replaces file-based saves for web deployment.
"""

import json
import logging
from typing import Dict, Any, Optional
from utils.constants import *
from config import PLAYER_BASE_SPEED

logger = logging.getLogger(__name__)

class WebSaveManager:
    """Handles saving and loading game state using browser localStorage"""

    def __init__(self, save_key="simple_roguelike_save"):
        """Initialize the web save manager with localStorage key"""
        self.save_key = save_key

        # Define required fields for validation
        self.required_fields = ["current_level", "score", "high_score"]

        # Define valid ranges for numeric fields
        self.valid_ranges = {
            "current_level": (1, 100),  # Level 1 to 100
            "score": (0, 1000000),      # 0 to 1,000,000
            "high_score": (0, 1000000), # 0 to 1,000,000
        }

        # Define valid ranges for player stats
        self.player_valid_ranges = {
            "health": (1, PLAYER_HEALTH * 10),
            "max_health": (PLAYER_HEALTH, PLAYER_HEALTH * 10),
            "damage": (PLAYER_DAMAGE, PLAYER_DAMAGE * 10),
            "speed": (1, PLAYER_BASE_SPEED * 5),
            "fire_rate": (100, PLAYER_FIRE_RATE * 2),
            "level": (1, 100),
            "xp": (0, 100000),
            "xp_to_next_level": (100, 1000000),
            "upgrade_points": (0, 100)
        }

        # Define required fields for progression data
        self.progression_fields = ["skill_tree", "equipment_manager", "achievement_manager", "stats"]

    def _get_local_storage(self):
        """Get localStorage object (web-specific)"""
        try:
            # In web environment, this will be available
            import js
            return js.localStorage
        except ImportError:
            # Fallback for desktop testing - use a simple dict
            if not hasattr(self, '_fallback_storage'):
                self._fallback_storage = {}
            return self._fallback_storage

    def _local_storage_get_item(self, key: str) -> Optional[str]:
        """Get item from localStorage with fallback"""
        try:
            storage = self._get_local_storage()
            if hasattr(storage, 'getItem'):
                return storage.getItem(key)
            else:
                # Fallback storage
                return storage.get(key)
        except Exception as e:
            logger.error(f"Error accessing localStorage: {e}")
            return None

    def _local_storage_set_item(self, key: str, value: str) -> bool:
        """Set item in localStorage with fallback"""
        try:
            storage = self._get_local_storage()
            if hasattr(storage, 'setItem'):
                storage.setItem(key, value)
            else:
                # Fallback storage
                storage[key] = value
            return True
        except Exception as e:
            logger.error(f"Error setting localStorage: {e}")
            return False

    def _local_storage_remove_item(self, key: str) -> bool:
        """Remove item from localStorage with fallback"""
        try:
            storage = self._get_local_storage()
            if hasattr(storage, 'removeItem'):
                storage.removeItem(key)
            else:
                # Fallback storage
                if key in storage:
                    del storage[key]
            return True
        except Exception as e:
            logger.error(f"Error removing from localStorage: {e}")
            return False

    def save_game(self, game_data: Dict[str, Any]) -> bool:
        """
        Save game data to localStorage with validation

        Args:
            game_data (dict): Dictionary containing game state to save

        Returns:
            bool: True if save was successful, False otherwise
        """
        # Validate data before saving
        if not self._validate_game_data(game_data):
            logger.error("Invalid game data format")
            return False

        try:
            json_data = json.dumps(game_data)
            return self._local_storage_set_item(self.save_key, json_data)
        except Exception as e:
            logger.error(f"Error saving game: {e}")
            return False

    def load_game(self) -> Optional[Dict[str, Any]]:
        """
        Load game data from localStorage with validation

        Returns:
            dict: Game data if load was successful, None otherwise
        """
        try:
            json_data = self._local_storage_get_item(self.save_key)
            if not json_data:
                return None

            data = json.loads(json_data)

            # Validate and sanitize loaded data
            if not self._validate_game_data(data):
                logger.warning("Save data contains invalid data, using defaults")
                return self._create_default_save()

            return data
        except json.JSONDecodeError:
            logger.error("Save data is corrupted (invalid JSON)")
            return None
        except Exception as e:
            logger.error(f"Unexpected error loading game: {e}")
            return None

    def save_exists(self) -> bool:
        """
        Check if a save file exists in localStorage

        Returns:
            bool: True if save exists, False otherwise
        """
        try:
            json_data = self._local_storage_get_item(self.save_key)
            return json_data is not None
        except Exception as e:
            logger.error(f"Error checking save existence: {e}")
            return False

    def delete_save(self) -> bool:
        """
        Delete the save file from localStorage

        Returns:
            bool: True if deletion was successful, False otherwise
        """
        try:
            return self._local_storage_remove_item(self.save_key)
        except Exception as e:
            logger.error(f"Error deleting save: {e}")
            return False

    def _validate_game_data(self, data: Dict[str, Any]) -> bool:
        """
        Validate game data structure and values

        Args:
            data (dict): Game data to validate

        Returns:
            bool: True if data is valid, False otherwise
        """
        # Check if data is a dictionary
        if not isinstance(data, dict):
            return False

        # Check for required fields
        for field in self.required_fields:
            if field not in data:
                logger.error(f"Missing required field: {field}")
                return False

        # Validate numeric fields
        for field, (min_val, max_val) in self.valid_ranges.items():
            if field in data:
                # Ensure field is numeric
                if not isinstance(data[field], (int, float)):
                    data[field] = min_val
                # Ensure field is within valid range
                data[field] = max(min_val, min(data[field], max_val))

        # Validate player data if present
        if "player_data" in data and isinstance(data["player_data"], dict):
            player_data = data["player_data"]

            for field, (min_val, max_val) in self.player_valid_ranges.items():
                if field in player_data:
                    # Ensure field is numeric
                    if not isinstance(player_data[field], (int, float)):
                        player_data[field] = min_val
                    # Ensure field is within valid range
                    player_data[field] = max(min_val, min(player_data[field], max_val))

            # Validate progression data if present
            if "progression_data" in player_data and isinstance(player_data["progression_data"], dict):
                progression_data = player_data["progression_data"]

                # Validate that progression data has expected structure
                for field in self.progression_fields:
                    if field not in progression_data:
                        logger.warning(f"Missing progression field: {field}")
                        # Don't fail validation, just warn - progression data is optional

                # Validate stats dictionary
                if "stats" in progression_data and isinstance(progression_data["stats"], dict):
                    stats = progression_data["stats"]
                    # Ensure all stat values are non-negative integers
                    for stat_name, value in stats.items():
                        if not isinstance(value, (int, float)) or value < 0:
                            stats[stat_name] = 0

        return True

    def _create_default_save(self) -> Dict[str, Any]:
        """
        Create a default save data structure

        Returns:
            dict: Default save data
        """
        return {
            "current_level": 1,
            "score": 0,
            "high_score": 0,
            "player_data": {
                "health": PLAYER_HEALTH,
                "max_health": PLAYER_HEALTH,
                "damage": PLAYER_DAMAGE,
                "speed": PLAYER_BASE_SPEED,
                "fire_rate": PLAYER_FIRE_RATE,
                "level": 1,
                "xp": 0,
                "xp_to_next_level": 100,
                "upgrade_points": 0
            }
        }
